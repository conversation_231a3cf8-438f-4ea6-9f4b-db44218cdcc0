import { useEffect, useRef } from 'react';
import { useSessionTimeoutStore } from './sessionStore';
import { useAuthStore } from './authStore';

/**
 * Hook to trigger an action (e.g., popup) after a timeout when betshopSettings changes.
 * @param onTimeout Callback to execute when timeout completes
 * @param modalOpen Whether the modal is currently open
 */
export function useBetshopSettingsTimeout(
    onTimeout: () => void,
    modalOpen: boolean
) {
    const betshopSettings = useSessionTimeoutStore((state) => state.betshopSettings);
    const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
    const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
    const waitTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

    const timeintervalTestRef = useRef<ReturnType<typeof setInterval> | null>(null);

    useEffect(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        if (timeintervalTestRef.current) {
            clearInterval(timeintervalTestRef.current);
        }
        // Only run session timeout logic if user is authenticated
        // Check for betshopSettings !== undefined instead of truthy check to handle 0 values
        waitTimeoutRef.current = setTimeout(() => {

            if (isAuthenticated && betshopSettings !== undefined && !modalOpen) {
                // getTimeoutMs should return minutes, convert to ms
                const minutes = Number(betshopSettings);

                // Validate that minutes is a positive number
                if (!isNaN(minutes) && minutes > 0) {
                    const ms = minutes * 60 * 1000;
                    let countdown = ms;
                    timeintervalTestRef.current = setInterval(() => {
                        countdown = countdown - 1000;
                    }, 1000);
                    timeoutRef.current = setTimeout(() => {
                        onTimeout();
                    }, ms);
                }
            }

        }, 1000);
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
            if (waitTimeoutRef.current) {
                clearTimeout(waitTimeoutRef.current);
            }
            if (timeintervalTestRef.current) {
                clearInterval(timeintervalTestRef.current);
            }
        };
    }, [isAuthenticated, betshopSettings, onTimeout, modalOpen]);
}

