import { useEffect, useRef } from 'react';
import { useSessionTimeoutStore } from './sessionStore';
import { useAuthStore } from './authStore';

/**
 * Hook to implement absolute session timeout based on login timestamp.
 * This ensures the timeout is not reset by user activity and is based on the exact time since login.
 * @param onTimeout Callback to execute when timeout completes
 * @param modalOpen Whether the modal is currently open
 */
export function useAbsoluteSessionTimeout(
    onTimeout: () => void,
    modalOpen: boolean
) {
    const betshopSettings = useSessionTimeoutStore((state) => state.betshopSettings);
    const { isAuthenticated, loginTimestamp } = useAuthStore((state) => ({
        isAuthenticated: state.isAuthenticated,
        loginTimestamp: state.loginTimestamp
    }));
    const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

    useEffect(() => {
        // Clear any existing timeout
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
        }

        // Only run session timeout logic if user is authenticated and modal is not open
        if (!isAuthenticated || modalOpen || betshopSettings === undefined) {
            return;
        }

        // Handle case where loginTimestamp is missing (backward compatibility)
        let effectiveLoginTimestamp = loginTimestamp;
        if (!effectiveLoginTimestamp) {
            // Set current time as login timestamp and update the store
            effectiveLoginTimestamp = Date.now();
            useAuthStore.getState().setAuth(
                useAuthStore.getState().token!,
                useAuthStore.getState().user!
            );
        }

        // Convert timeout from minutes to milliseconds
        const timeoutMinutes = Number(betshopSettings);

        // Validate that timeout is a positive number
        if (isNaN(timeoutMinutes) || timeoutMinutes <= 0) {
            return;
        }

        const timeoutMs = timeoutMinutes * 60 * 1000;
        const currentTime = Date.now();
        const elapsedTime = currentTime - effectiveLoginTimestamp;
        const remainingTime = timeoutMs - elapsedTime;

        // If session has already expired, trigger timeout immediately
        if (remainingTime <= 0) {
            onTimeout();
            return;
        }

        // Set timeout for the remaining time
        timeoutRef.current = setTimeout(() => {
            onTimeout();
        }, remainingTime);

        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
                timeoutRef.current = null;
            }
        };
    }, [isAuthenticated, betshopSettings, loginTimestamp, onTimeout, modalOpen]);
}

/**
 * @deprecated Use useAbsoluteSessionTimeout instead for proper absolute timeout behavior
 * Legacy hook that resets timeout on activity - kept for backward compatibility
 */
export function useBetshopSettingsTimeout(
    onTimeout: () => void,
    modalOpen: boolean
) {
    const betshopSettings = useSessionTimeoutStore((state) => state.betshopSettings);
    const isAuthenticated = useAuthStore((state) => state.isAuthenticated);
    const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
    const waitTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

    const timeintervalTestRef = useRef<ReturnType<typeof setInterval> | null>(null);

    useEffect(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        if (timeintervalTestRef.current) {
            clearInterval(timeintervalTestRef.current);
        }
        // Only run session timeout logic if user is authenticated
        // Check for betshopSettings !== undefined instead of truthy check to handle 0 values
        waitTimeoutRef.current = setTimeout(() => {

            if (isAuthenticated && betshopSettings !== undefined && !modalOpen) {
                // getTimeoutMs should return minutes, convert to ms
                const minutes = Number(betshopSettings);

                // Validate that minutes is a positive number
                if (!isNaN(minutes) && minutes > 0) {
                    const ms = minutes * 60 * 1000;
                    let countdown = ms;
                    timeintervalTestRef.current = setInterval(() => {
                        countdown = countdown - 1000;
                    }, 1000);
                    timeoutRef.current = setTimeout(() => {
                        onTimeout();
                    }, ms);
                }
            }

        }, 1000);
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
            if (waitTimeoutRef.current) {
                clearTimeout(waitTimeoutRef.current);
            }
            if (timeintervalTestRef.current) {
                clearInterval(timeintervalTestRef.current);
            }
        };
    }, [isAuthenticated, betshopSettings, onTimeout, modalOpen]);
}

