'use client';
import React, { useState } from 'react';
import { useAbsoluteSessionTimeout } from './useBetshopSettingsTimeout';
import { useAuthStore } from './authStore';
import { useRouter } from 'next/navigation';
import { AuthErrorModal } from '@/shared/components/ui-elements/alerts';

/**
 * Component that handles absolute session timeout based on login timestamp.
 * Shows a modal when the session expires and redirects to login page.
 */
export function SessionTimeoutHandler() {
    const [modalOpen, setModalOpen] = useState(false);

    const handleTimeout = () => {
        setModalOpen(true);
    };

    const router = useRouter();
    const handleClose = () => {
        setModalOpen(false);
        useAuthStore.getState().clearAuth();
        router.replace('/authentication/sign-in');
    };

    // Use the new absolute timeout hook that doesn't reset on activity
    useAbsoluteSessionTimeout(handleTimeout, modalOpen);

    return (
        <AuthErrorModal
            isOpen={modalOpen}
            onClose={handleClose}
        />
    );
}
