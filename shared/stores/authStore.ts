import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { User } from '@/shared/types/global';
import { setAuthCookies, clearAuthCookies, syncAuthFromCookies } from '@/shared/utils/authCookies';
import { saveTwoFactorState, loadTwoFactorState, clearTwoFactorState } from '@/shared/utils/twoFactorStorage';

interface AuthState {
    token: string | null;
    user: User | null;
    isAuthenticated: boolean;
    _hasHydrated: boolean;
    twoStepVerificationRequired: boolean;
    tempUserFor2FA: { id: number; email: string } | null;
    loginMessage: string | null;
    loginTimestamp: number | null; // Timestamp when user logged in (for absolute session timeout)

    // Actions
    setAuth: (token: string, user: User) => void;
    clearAuth: () => void;
    setHasHydrated: (state: boolean) => void;
    // New actions for 2FA flow
    setTwoStepRequired: (id: number, email: string, message: string) => void;
    clearTwoStep: () => void;
    // setFullAuth is used when PIN is validated
    setFullAuth: (token: string, user: User) => void;
    // Utility to ensure login timestamp exists (for backward compatibility)
    ensureLoginTimestamp: () => void;
}

export const useAuthStore = create<AuthState>()(
    persist(
        (set) => ({
            token: null,
            user: null,
            isAuthenticated: false,
            _hasHydrated: false,
            twoStepVerificationRequired: false, // Default to false
            tempUserFor2FA: null,
            loginMessage: null,
            loginTimestamp: null, // Initialize login timestamp

            setAuth: (token, user) => {
                // Set cookies for server-side access
                setAuthCookies(token, user);
                set({ token, user, isAuthenticated: true, loginTimestamp: Date.now() });
            },
            clearAuth: () => {
                // Clear cookies and session storage
                clearAuthCookies();
                clearTwoFactorState();
                set({ token: null, user: null, isAuthenticated: false, twoStepVerificationRequired: false, tempUserFor2FA: null, loginMessage: null, loginTimestamp: null });
            },
            setHasHydrated: (state) => set({ _hasHydrated: state }),

            // Action to set 2FA required state
            setTwoStepRequired: (id, email, message) => {
                const newState = {
                    twoStepVerificationRequired: true,
                    tempUserFor2FA: { id, email },
                    loginMessage: message,
                    isAuthenticated: false, // Ensure not fully authenticated yet
                    token: null, // Ensure token is not set yet
                    user: null,
                };

                // Save 2FA state to session storage for persistence during navigation
                try {
                    saveTwoFactorState({
                        twoStepVerificationRequired: true,
                        tempUserFor2FA: { id, email },
                        loginMessage: message,
                    });
                } catch (error) {
                    // eslint-disable-next-line no-console
                    console.error('Failed to save 2FA state:', error);
                }

                set(newState);
            },
            // Action to clear 2FA state (e.g., if user navigates away or refreshes login page)
            clearTwoStep: () => {
                // Clear from session storage as well
                clearTwoFactorState();
                set({
                    twoStepVerificationRequired: false,
                    tempUserFor2FA: null,
                    loginMessage: null,
                });
            },
            // Action for when 2FA is successfully completed
            setFullAuth: (token, user) => {
                // Set cookies for server-side access
                setAuthCookies(token, user);
                // Clear 2FA state from session storage
                clearTwoFactorState();
                set({
                    token,
                    user,
                    isAuthenticated: true,
                    twoStepVerificationRequired: false, // 2FA no longer required
                    tempUserFor2FA: null,
                    loginMessage: null,
                    loginTimestamp: Date.now(), // Set login timestamp for session timeout
                });
            },
            // Utility function to ensure login timestamp exists (for backward compatibility)
            ensureLoginTimestamp: () => {
                const state = useAuthStore.getState();
                if (state.isAuthenticated && !state.loginTimestamp) {
                    set({ loginTimestamp: Date.now() });
                }
            },
        }),
        {
            name: 'auth-storage',
            storage: createJSONStorage(() => localStorage),
            // Only persist full authentication state including login timestamp for session timeout
            partialize: (state) => ({ token: state.token, user: state.user, isAuthenticated: state.isAuthenticated, loginTimestamp: state.loginTimestamp }),
            onRehydrateStorage: () => (state) => {
                // Sync authentication state from cookies on hydration
                if (typeof window !== 'undefined') {
                    const { token, user } = syncAuthFromCookies();
                    if (token && user && state) {
                        // Update state with cookie data if available
                        state.token = token;
                        state.user = user;
                        state.isAuthenticated = true;
                        // Clear 2FA state if user is already fully authenticated
                        state.twoStepVerificationRequired = false;
                        state.tempUserFor2FA = null;
                        state.loginMessage = null;
                        // Set login timestamp if not already set (for backward compatibility)
                        // Use a reasonable fallback - assume login was recent if no timestamp exists
                        if (!state.loginTimestamp) {
                            state.loginTimestamp = Date.now();
                        }
                        clearTwoFactorState(); // Clear session storage as well
                    } else {
                        // If not fully authenticated, check for 2FA state in session storage
                        const twoFactorState = loadTwoFactorState();
                        if (twoFactorState && state) {
                            state.twoStepVerificationRequired = twoFactorState.twoStepVerificationRequired;
                            state.tempUserFor2FA = twoFactorState.tempUserFor2FA;
                            state.loginMessage = twoFactorState.loginMessage;
                        }
                    }
                }
                state?.setHasHydrated(true);
            },
        }
    )
);
