"use client";

import React, { useState } from 'react';
import { UserManagementModal } from '@/shared/UI/components';
import { CreateUserData, EditUserData } from '@/shared/query';

export default function TestModalPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleSubmit = async (formData: CreateUserData | EditUserData) => {
    // console.log('Form submitted with data:', formData);
    // Check if rfidToken is included
    if ('rfidToken' in formData) {
      // console.log('RFID Token:', formData.rfidToken);
    }
    setIsModalOpen(false);
  };

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Test User Management Modal</h1>
      <button
        onClick={() => setIsModalOpen(true)}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      >
        Open Create User Modal
      </button>

      <UserManagementModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        mode="create"
        onSubmit={handleSubmit}
        isLoading={false}
      />
    </div>
  );
}
