"use client";

import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/shared/stores/authStore';
import { useSessionTimeoutStore } from '@/shared/stores/sessionStore';

export default function TestSessionTimeoutPage() {
    const { isAuthenticated, loginTimestamp, user } = useAuthStore();
    const { betshopSettings } = useSessionTimeoutStore();
    const [currentTime, setCurrentTime] = useState(Date.now());
    const [remainingTime, setRemainingTime] = useState<number | null>(null);

    // Update current time every second
    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(Date.now());
        }, 1000);

        return () => clearInterval(interval);
    }, []);

    // Calculate remaining time
    useEffect(() => {
        if (isAuthenticated && loginTimestamp && betshopSettings) {
            const timeoutMs = Number(betshopSettings) * 60 * 1000;
            const elapsedTime = currentTime - loginTimestamp;
            const remaining = timeoutMs - elapsedTime;
            setRemainingTime(Math.max(0, remaining));
        } else {
            setRemainingTime(null);
        }
    }, [isAuthenticated, loginTimestamp, betshopSettings, currentTime]);

    const formatTime = (ms: number) => {
        const totalSeconds = Math.floor(ms / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    };

    const formatTimestamp = (timestamp: number) => {
        return new Date(timestamp).toLocaleString();
    };

    if (!isAuthenticated) {
        return (
            <div className="p-8">
                <h1 className="text-2xl font-bold mb-4">Session Timeout Test</h1>
                <p className="text-red-600">Please log in to test session timeout functionality.</p>
            </div>
        );
    }

    return (
        <div className="p-8">
            <h1 className="text-2xl font-bold mb-6">Session Timeout Test</h1>
            
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 className="text-lg font-semibold mb-4">Authentication Status</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700">User:</label>
                        <p className="text-sm text-gray-900">{user?.email || 'N/A'}</p>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Authenticated:</label>
                        <p className={`text-sm ${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                            {isAuthenticated ? 'Yes' : 'No'}
                        </p>
                    </div>
                </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 className="text-lg font-semibold mb-4">Session Timeout Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Login Time:</label>
                        <p className="text-sm text-gray-900">
                            {loginTimestamp ? formatTimestamp(loginTimestamp) : 'Not available'}
                        </p>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Current Time:</label>
                        <p className="text-sm text-gray-900">{formatTimestamp(currentTime)}</p>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Timeout Duration:</label>
                        <p className="text-sm text-gray-900">
                            {betshopSettings ? `${betshopSettings} minutes` : 'Not loaded'}
                        </p>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Time Remaining:</label>
                        <p className={`text-sm font-mono ${remainingTime && remainingTime < 60000 ? 'text-red-600' : 'text-gray-900'}`}>
                            {remainingTime !== null ? formatTime(remainingTime) : 'Calculating...'}
                        </p>
                    </div>
                </div>
            </div>

            <div className="bg-blue-50 rounded-lg p-6">
                <h2 className="text-lg font-semibold mb-4">Test Instructions</h2>
                <ol className="list-decimal list-inside space-y-2 text-sm">
                    <li>This page shows your current session timeout status in real-time</li>
                    <li>The "Time Remaining" countdown shows exactly when your session will expire</li>
                    <li>The timeout is <strong>absolute</strong> - it won't reset based on your activity</li>
                    <li>Try refreshing the page - the countdown should continue from where it left off</li>
                    <li>When the timer reaches 0:00, you should see a session timeout modal</li>
                    <li>After timeout, you'll be redirected to the login page</li>
                </ol>
            </div>

            {remainingTime !== null && remainingTime < 60000 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-6">
                    <div className="flex items-center">
                        <div className="flex-shrink-0">
                            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                        </div>
                        <div className="ml-3">
                            <h3 className="text-sm font-medium text-red-800">
                                Session Expiring Soon!
                            </h3>
                            <div className="mt-2 text-sm text-red-700">
                                <p>Your session will expire in less than 1 minute. Please save any work.</p>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
