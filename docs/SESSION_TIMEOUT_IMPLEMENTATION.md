# Absolute Session Timeout Implementation

## Overview

This document describes the implementation of absolute session timeout functionality that enforces a fixed session duration from login time, regardless of user activity.

## Requirements Fulfilled

✅ **Backend-driven timeout**: Uses `cashier_web_timeout` from betshop settings API (in minutes)  
✅ **Absolute timeout behavior**: Timeout calculated from login timestamp, not reset by activity  
✅ **No activity-based extension**: Timer runs independently of user interactions  
✅ **Timeout enforcement**: Shows modal and redirects to login when session expires  
✅ **Persistent across refreshes**: Login timestamp stored in localStorage  

## Implementation Details

### 1. Auth Store Changes (`shared/stores/authStore.ts`)

- Added `loginTimestamp: number | null` to track when user logged in
- Updated `setAuth` and `setFullAuth` to record `Date.now()` as login timestamp
- Updated `clearAuth` to clear the timestamp
- Added timestamp to persistence configuration for localStorage
- Added `ensureLoginTimestamp()` utility for backward compatibility

### 2. New Absolute Timeout Hook (`shared/stores/useBetshopSettingsTimeout.ts`)

- Created `useAbsoluteSessionTimeout` hook that implements true absolute timeout
- Calculates remaining time: `(timeoutMinutes * 60 * 1000) - (Date.now() - loginTimestamp)`
- Sets single timeout for remaining duration, doesn't reset on dependency changes
- Handles edge cases like missing timestamps with backward compatibility
- Kept old hook as deprecated for compatibility

### 3. Updated Session Handler (`shared/stores/SessionTimeoutHandler.tsx`)

- Switched to use `useAbsoluteSessionTimeout` instead of the old hook
- Maintains same modal and redirect behavior

## How It Works

```typescript
// Calculate remaining time from login timestamp
const timeoutMs = timeoutMinutes * 60 * 1000;
const currentTime = Date.now();
const elapsedTime = currentTime - loginTimestamp;
const remainingTime = timeoutMs - elapsedTime;

// If session expired, trigger timeout immediately
if (remainingTime <= 0) {
    onTimeout();
    return;
}

// Set timeout for remaining time (not full duration)
setTimeout(() => onTimeout(), remainingTime);
```

## Testing

### Manual Testing

1. **Login to the application** - This sets the `loginTimestamp`
2. **Visit `/test-session-timeout`** - Shows real-time countdown
3. **Verify absolute behavior**:
   - Refresh page - countdown continues from same point
   - Interact with app - countdown doesn't reset
   - Wait for timeout - modal appears and redirects to login

### Test Page Features

The test page (`/test-session-timeout`) displays:
- Current authentication status
- Login timestamp and current time
- Configured timeout duration from backend
- Real-time countdown to session expiry
- Warning when less than 1 minute remains

### Expected Behavior

1. **On Login**: `loginTimestamp` is set to current time
2. **During Session**: Countdown shows exact remaining time
3. **On Refresh**: Countdown continues from correct remaining time
4. **On Activity**: Countdown is NOT affected by user interactions
5. **On Timeout**: Modal appears, user is logged out and redirected

## Backward Compatibility

- Existing users without `loginTimestamp` get it set automatically
- Old `useBetshopSettingsTimeout` hook remains functional but deprecated
- All existing functionality preserved

## Configuration

The timeout duration comes from the backend API:
- **API**: Betshop Settings API
- **Key**: `cashier_web_timeout`
- **Unit**: Minutes
- **Storage**: `useSessionTimeoutStore.betshopSettings`

## Troubleshooting

### Common Issues

1. **Timeout not working**: Check if `betshopSettings` is loaded from API
2. **Countdown resets**: Verify using `useAbsoluteSessionTimeout`, not old hook
3. **No timestamp**: Check if `loginTimestamp` exists in auth store
4. **Infinite loops**: Ensure not calling store setters inside useEffect

### Debug Information

Check browser localStorage for auth data:
```javascript
// In browser console
JSON.parse(localStorage.getItem('auth-storage'))
```

Should contain:
```json
{
  "state": {
    "token": "...",
    "user": {...},
    "isAuthenticated": true,
    "loginTimestamp": 1640995200000
  }
}
```

## Files Modified

- `shared/stores/authStore.ts` - Added login timestamp tracking
- `shared/stores/useBetshopSettingsTimeout.ts` - New absolute timeout hook
- `shared/stores/SessionTimeoutHandler.tsx` - Updated to use new hook
- `app/test-session-timeout/page.tsx` - Test page for verification
